import React, { useState, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import {
  Eye,
  Target,
  Settings,
  Play,
  Square,
  Trash2,
  Download,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { elevationService, ViewshedResult, ElevationServiceStatus } from '@/services/elevationService';

interface ViewshedAnalysisProps {
  map: maplibregl.Map;
  isVisible?: boolean;
  className?: string;
}

interface ViewshedPoint {
  id: string;
  coordinates: [number, number];
  height: number;
  radius: number;
  angle: number;
  direction: number;
  visible: boolean;
}

interface ViewshedSettings {
  observerHeight: number;
  targetHeight: number;
  maxDistance: number;
  verticalAngle: number;
  horizontalAngle: number;
  resolution: number;
  elevationSource: 'usgs' | 'open-elevation' | 'open-meteo';
  requireRealData: boolean;
}

const ViewshedAnalysis: React.FC<ViewshedAnalysisProps> = ({
  map,
  isVisible = true,
  className = ''
}) => {
  const [isActive, setIsActive] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [show3DView, setShow3DView] = useState(false);
  const [viewsheds, setViewsheds] = useState<ViewshedPoint[]>([]);
  const [selectedViewshed, setSelectedViewshed] = useState<string | null>(null);

  const markersRef = useRef<Map<string, maplibregl.Marker>>(new Map());
  const layersRef = useRef<Set<string>>(new Set());

  const [settings, setSettings] = useState<ViewshedSettings>({
    observerHeight: 1.7, // meters
    targetHeight: 1.7,   // meters
    maxDistance: 1000,   // meters
    verticalAngle: 60,   // degrees
    horizontalAngle: 360, // degrees (full circle)
    resolution: 25,      // analysis points (reduced for real API calls)
    elevationSource: 'open-meteo', // real elevation data source
    requireRealData: true // only allow real elevation data
  });

  const [serviceStatus, setServiceStatus] = useState<ElevationServiceStatus | null>(null);
  const [dataQuality, setDataQuality] = useState<{
    realDataPercentage: number;
    issues: string[];
  } | null>(null);

  // Toggle viewshed analysis mode
  const toggleAnalysis = useCallback(() => {
    setIsActive(!isActive);

    if (isActive) {
      // Clean up when deactivating
      clearAllViewsheds();
      map.getCanvas().style.cursor = '';
    } else {
      // Set up click handler for adding viewshed points
      map.getCanvas().style.cursor = 'crosshair';
    }
  }, [isActive, map]);

  // Toggle 3D view
  const toggle3DView = useCallback(() => {
    const new3DMode = !show3DView;
    setShow3DView(new3DMode);

    if (new3DMode) {
      // Enable 3D terrain view
      map.setPitch(60);
      map.setBearing(45);

      // Add terrain layer if available
      if (!map.getSource('mapbox-dem')) {
        map.addSource('mapbox-dem', {
          type: 'raster-dem',
          url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
          tileSize: 512,
          maxzoom: 14
        });

        map.setTerrain({ source: 'mapbox-dem', exaggeration: 1.5 });
      }
    } else {
      // Disable 3D terrain view
      map.setPitch(0);
      map.setBearing(0);

      // Remove terrain
      if (map.getTerrain()) {
        map.setTerrain(null);
      }
    }
  }, [show3DView, map]);

  // Handle map click to add viewshed point
  const handleMapClick = useCallback((e: maplibregl.MapMouseEvent) => {
    if (!isActive) return;

    const { lng, lat } = e.lngLat;
    const newViewshed: ViewshedPoint = {
      id: `viewshed-${Date.now()}`,
      coordinates: [lng, lat],
      height: settings.observerHeight,
      radius: settings.maxDistance,
      angle: settings.horizontalAngle,
      direction: 0, // North
      visible: true
    };

    addViewshedPoint(newViewshed);
  }, [isActive, settings]);

  // Add viewshed point
  const addViewshedPoint = (viewshed: ViewshedPoint) => {
    setViewsheds(prev => [...prev, viewshed]);

    // Create marker for viewshed point
    const markerElement = createViewshedMarker(viewshed);
    const marker = new maplibregl.Marker({ element: markerElement })
      .setLngLat(viewshed.coordinates)
      .addTo(map);

    markersRef.current.set(viewshed.id, marker);

    // Calculate and display viewshed
    calculateViewshed(viewshed);
  };

  // Create viewshed marker element
  const createViewshedMarker = (viewshed: ViewshedPoint): HTMLElement => {
    const element = document.createElement('div');
    element.className = 'viewshed-marker';
    element.style.cssText = `
      width: 24px;
      height: 24px;
      background: #FFD700;
      border: 2px solid #FFF;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #000;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    `;
    element.innerHTML = '👁';

    // Add click handler
    element.addEventListener('click', (e) => {
      e.stopPropagation();
      setSelectedViewshed(viewshed.id);
    });

    return element;
  };

  // Calculate viewshed using REAL elevation data only
  const calculateViewshed = async (viewshed: ViewshedPoint) => {
    console.log('🎯 Starting PROFESSIONAL viewshed calculation with REAL elevation data');
    console.log('Settings:', {
      elevationSource: settings.elevationSource,
      requireRealData: settings.requireRealData,
      coordinates: viewshed.coordinates,
      radius: viewshed.radius,
      resolution: settings.resolution
    });

    setIsAnalyzing(true);
    setDataQuality(null);

    try {
      // Test elevation service first
      const serviceWorking = await elevationService.testService(settings.elevationSource);
      const status = elevationService.getServiceStatus();
      setServiceStatus(status);

      if (!serviceWorking && settings.requireRealData) {
        throw new Error(`Elevation service ${settings.elevationSource} is not available and real data is required`);
      }

      // Generate viewshed using real elevation data
      const viewshedData = await generateRealViewshed(viewshed);

      if (!viewshedData) {
        throw new Error('Failed to generate viewshed - insufficient real elevation data');
      }

      // Add viewshed layers to map
      await addViewshedToMap(viewshed, viewshedData);

      console.log('✅ Professional viewshed analysis completed successfully');

    } catch (error) {
      console.error('❌ Viewshed calculation failed:', error);

      // Show error to user
      setDataQuality({
        realDataPercentage: 0,
        issues: [error instanceof Error ? error.message : 'Unknown error occurred']
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Generate viewshed using REAL elevation data - NO SIMULATION
  const generateRealViewshed = async (viewshed: ViewshedPoint) => {
    const { coordinates, radius, angle, direction } = viewshed;
    const [centerLng, centerLat] = coordinates;

    console.log(`🗺️ Generating REAL viewshed analysis for ${centerLat}, ${centerLng}`);

    const points: number[][] = [[centerLng, centerLat]]; // Start from center
    const analysisResults: ViewshedResult[] = [];

    // Convert to radians
    const startAngle = (direction - angle / 2) * Math.PI / 180;
    const endAngle = (direction + angle / 2) * Math.PI / 180;
    const numPoints = Math.min(settings.resolution, 50); // Limit for API rate limits
    const angleStep = (endAngle - startAngle) / numPoints;

    console.log(`📊 Analyzing ${numPoints} directions with REAL elevation data...`);

    // Calculate visibility for each direction using REAL elevation data
    for (let i = 0; i <= numPoints; i++) {
      const currentAngle = startAngle + (angleStep * i);

      // Calculate target point at max distance
      const deltaLat = (radius / 111320) * Math.cos(currentAngle);
      const deltaLng = (radius / (111320 * Math.cos(centerLat * Math.PI / 180))) * Math.sin(currentAngle);

      const targetLat = centerLat + deltaLat;
      const targetLng = centerLng + deltaLng;

      try {
        // Calculate line-of-sight using REAL elevation service
        const result = await elevationService.calculateLineOfSight(
          centerLat, centerLng, settings.observerHeight,
          targetLat, targetLng, settings.targetHeight,
          settings.elevationSource
        );

        if (result) {
          analysisResults.push(result);

          // Determine the visible distance for this direction
          let visibleDistance = radius;
          if (!result.visible && result.obstruction) {
            visibleDistance = result.obstruction.distance;
          }

          // Convert visible distance to coordinates
          const visibleDeltaLat = (visibleDistance / 111320) * Math.cos(currentAngle);
          const visibleDeltaLng = (visibleDistance / (111320 * Math.cos(centerLat * Math.PI / 180))) * Math.sin(currentAngle);

          points.push([
            centerLng + visibleDeltaLng,
            centerLat + visibleDeltaLat
          ]);

          console.log(`📍 Direction ${Math.round(currentAngle * 180 / Math.PI)}°: ${result.visible ? '✅ Visible' : '❌ Blocked'} at ${Math.round(result.distance)}m`);
        } else {
          // If we can't get real elevation data, we cannot provide accurate analysis
          console.error(`❌ No real elevation data for direction ${Math.round(currentAngle * 180 / Math.PI)}°`);

          if (settings.requireRealData) {
            throw new Error(`Real elevation data required but not available for direction ${Math.round(currentAngle * 180 / Math.PI)}°`);
          }

          // Skip this direction rather than using fake data
          continue;
        }
      } catch (error) {
        console.error(`❌ Failed to analyze direction ${Math.round(currentAngle * 180 / Math.PI)}°:`, error);

        if (settings.requireRealData) {
          throw error;
        }

        // Skip this direction
        continue;
      }
    }

    if (analysisResults.length === 0) {
      console.error('❌ No valid analysis results - cannot generate viewshed');
      return null;
    }

    points.push([centerLng, centerLat]); // Close the polygon

    // Validate data quality
    const visibleCount = analysisResults.filter(r => r.visible).length;
    const realDataCount = analysisResults.filter(r => r.dataSource === 'real-api').length;
    const realDataPercentage = (realDataCount / analysisResults.length) * 100;

    console.log(`📊 Analysis complete: ${visibleCount}/${analysisResults.length} directions visible`);
    console.log(`📊 Data quality: ${realDataPercentage.toFixed(1)}% real elevation data`);

    // Update data quality state
    setDataQuality({
      realDataPercentage,
      issues: realDataPercentage < 100 ? [`${(100 - realDataPercentage).toFixed(1)}% of analysis used non-real data`] : []
    });

    return {
      type: 'Feature',
      geometry: {
        type: 'Polygon',
        coordinates: [points]
      },
      properties: {
        viewshedId: viewshed.id,
        analysisMode: 'real-elevation',
        visibleDirections: visibleCount,
        totalDirections: analysisResults.length,
        realDataPercentage: realDataPercentage,
        elevationSource: settings.elevationSource
      }
    };
  };

  // Add viewshed visualization to map
  const addViewshedToMap = async (viewshed: ViewshedPoint, viewshedData: any) => {
    const sourceId = `viewshed-source-${viewshed.id}`;
    const layerId = `viewshed-layer-${viewshed.id}`;

    // Remove existing layers if they exist
    if (map.getLayer(layerId)) {
      map.removeLayer(layerId);
    }
    if (map.getLayer(`${layerId}-outline`)) {
      map.removeLayer(`${layerId}-outline`);
    }
    if (map.getSource(sourceId)) {
      map.removeSource(sourceId);
    }

    // Add source
    map.addSource(sourceId, {
      type: 'geojson',
      data: viewshedData
    });

    // Professional viewshed visualization
    map.addLayer({
      id: layerId,
      type: 'fill',
      source: sourceId,
      paint: {
        'fill-color': '#FFD700', // Gold color for professional appearance
        'fill-opacity': 0.25
      }
    });

    // Add outline layer
    map.addLayer({
      id: `${layerId}-outline`,
      type: 'line',
      source: sourceId,
      paint: {
        'line-color': '#FFD700',
        'line-width': 2,
        'line-opacity': 0.9
      }
    });

    layersRef.current.add(layerId);
    layersRef.current.add(`${layerId}-outline`);
  };

  // Test elevation service connectivity
  const testElevationService = async () => {
    console.log('🧪 Testing elevation service connectivity...');
    setIsAnalyzing(true);

    try {
      const isWorking = await elevationService.testService(settings.elevationSource);
      const status = elevationService.getServiceStatus();
      setServiceStatus(status);

      if (isWorking) {
        console.log('✅ Elevation service is working correctly');
        setDataQuality({
          realDataPercentage: 100,
          issues: []
        });
      } else {
        console.log('❌ Elevation service is not available');
        setDataQuality({
          realDataPercentage: 0,
          issues: [`${settings.elevationSource} elevation service is not available`]
        });
      }
    } catch (error) {
      console.error('❌ Elevation service test failed:', error);
      setDataQuality({
        realDataPercentage: 0,
        issues: [error instanceof Error ? error.message : 'Service test failed']
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Remove viewshed
  const removeViewshed = (viewshedId: string) => {
    // Remove marker
    const marker = markersRef.current.get(viewshedId);
    if (marker) {
      marker.remove();
      markersRef.current.delete(viewshedId);
    }

    // Remove layers
    const layerId = `viewshed-layer-${viewshedId}`;
    const sourceId = `viewshed-source-${viewshedId}`;

    if (map.getLayer(layerId)) {
      map.removeLayer(layerId);
      layersRef.current.delete(layerId);
    }
    if (map.getLayer(`${layerId}-outline`)) {
      map.removeLayer(`${layerId}-outline`);
      layersRef.current.delete(`${layerId}-outline`);
    }
    if (map.getSource(sourceId)) {
      map.removeSource(sourceId);
    }

    // Remove from state
    setViewsheds(prev => prev.filter(v => v.id !== viewshedId));

    if (selectedViewshed === viewshedId) {
      setSelectedViewshed(null);
    }
  };

  // Clear all viewsheds
  const clearAllViewsheds = () => {
    viewsheds.forEach(viewshed => removeViewshed(viewshed.id));
    map.getCanvas().style.cursor = '';
  };

  // Export viewshed data
  const exportViewsheds = () => {
    const data = {
      viewsheds: viewsheds,
      settings: settings,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `viewshed-analysis-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Set up map event listeners
  React.useEffect(() => {
    if (isActive) {
      map.on('click', handleMapClick);
    } else {
      map.off('click', handleMapClick);
    }

    return () => {
      map.off('click', handleMapClick);
    };
  }, [isActive, handleMapClick, map]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      clearAllViewsheds();
    };
  }, []);

  // Don't render if not visible
  if (!isVisible) return null;

  return (
    <div className={`absolute top-20 right-4 z-40 ${className}`}>
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm w-[160px]">
        {/* Header */}
        <div className="flex items-center justify-between p-2 border-b border-gray-600">
          <div className="flex items-center space-x-1">
            <Eye size={12} className="text-yellow-400" />
            <span className="text-[10px] font-mono text-gray-300 font-semibold">
              PROFESSIONAL VIEWSHED
            </span>
            {serviceStatus?.available && (
              <CheckCircle size={8} className="text-green-400" title="Real Elevation Data Available" />
            )}
            {serviceStatus?.available === false && (
              <XCircle size={8} className="text-red-400" title="Elevation Service Unavailable" />
            )}
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={testElevationService}
              className="p-1 h-5 w-5 text-gray-400 hover:text-white"
              title="Test Elevation Service"
            >
              <Target size={10} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowSettings(!showSettings)}
              className="p-1 h-5 w-5 text-gray-400 hover:text-white"
              title="Settings"
            >
              <Settings size={10} />
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="p-2 space-y-2">
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant={isActive ? "default" : "ghost"}
              onClick={toggleAnalysis}
              className="flex-1 text-[10px] py-1 h-6"
            >
              {isActive ? <Square size={10} /> : <Play size={10} />}
              <span className="ml-1">
                {isActive ? 'Stop' : 'Start'}
              </span>
            </Button>
          </div>

          {isAnalyzing && (
            <div className="text-xs text-yellow-400 text-center">
              Calculating viewshed...
            </div>
          )}

          {/* Settings panel */}
          {showSettings && (
            <div className="space-y-1 border-t border-gray-600 pt-2">
              <div className="text-[9px] text-gray-400 mb-1">Professional Settings</div>

              {/* Elevation Data Source */}
              <div className="space-y-1">
                <label className="text-[9px] text-gray-400">Real Elevation Source</label>
                <select
                  value={settings.elevationSource}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    elevationSource: e.target.value as 'usgs' | 'open-elevation' | 'open-meteo'
                  }))}
                  className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                >
                  <option value="open-meteo">Open-Meteo (Global, High Quality)</option>
                  <option value="open-elevation">Open Elevation (Global, SRTM)</option>
                  <option value="usgs">USGS (US Only, Highest Quality)</option>
                </select>
              </div>

              {/* Data Quality Requirements */}
              <div className="space-y-1">
                <div className="flex items-center space-x-1">
                  <input
                    type="checkbox"
                    id="requireRealData"
                    checked={settings.requireRealData}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      requireRealData: e.target.checked
                    }))}
                    className="w-3 h-3"
                  />
                  <label htmlFor="requireRealData" className="text-[9px] text-gray-400">
                    Require Real Data Only
                  </label>
                </div>
              </div>

              {/* Service Status */}
              {serviceStatus && (
                <div className="space-y-1">
                  <div className="text-[9px] text-gray-400">Service Status</div>
                  <div className={`text-[8px] p-1 rounded ${
                    serviceStatus.available ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
                  }`}>
                    {serviceStatus.available ? '✅ Available' : '❌ Unavailable'}
                    {serviceStatus.lastSuccessful && (
                      <div>Last: {serviceStatus.lastSuccessful.toLocaleTimeString()}</div>
                    )}
                  </div>
                </div>
              )}

              {/* Data Quality Indicator */}
              {dataQuality && (
                <div className="space-y-1">
                  <div className="text-[9px] text-gray-400">Data Quality</div>
                  <div className={`text-[8px] p-1 rounded ${
                    dataQuality.realDataPercentage >= 90 ? 'bg-green-900 text-green-300' :
                    dataQuality.realDataPercentage >= 70 ? 'bg-yellow-900 text-yellow-300' :
                    'bg-red-900 text-red-300'
                  }`}>
                    {dataQuality.realDataPercentage.toFixed(1)}% Real Data
                    {dataQuality.issues.length > 0 && (
                      <div className="mt-1">
                        {dataQuality.issues.map((issue, i) => (
                          <div key={i}>⚠️ {issue}</div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 gap-1 text-[9px]">
                <div>
                  <label className="text-gray-400">Observer Height (m)</label>
                  <input
                    type="number"
                    value={settings.observerHeight}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      observerHeight: parseFloat(e.target.value) || 1.7
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                    step="0.1"
                    min="0"
                  />
                </div>

                <div>
                  <label className="text-gray-400">Max Distance (m)</label>
                  <input
                    type="number"
                    value={settings.maxDistance}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      maxDistance: parseInt(e.target.value) || 1000
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                    step="100"
                    min="100"
                    max="10000"
                  />
                </div>

                <div className="grid grid-cols-2 gap-1">
                  <div>
                    <label className="text-gray-400">H.Angle</label>
                    <input
                      type="number"
                      value={settings.horizontalAngle}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        horizontalAngle: parseInt(e.target.value) || 360
                      }))}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                      step="15"
                      min="15"
                      max="360"
                    />
                  </div>

                  <div>
                    <label className="text-gray-400">Resolution</label>
                    <input
                      type="number"
                      value={settings.resolution}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        resolution: parseInt(e.target.value) || 25
                      }))}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                      step="5"
                      min="10"
                      max="50"
                      title="Number of analysis directions (lower = faster)"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Viewshed list */}
          {viewsheds.length > 0 && (
            <div className="space-y-1 border-t border-gray-600 pt-2">
              <div className="flex items-center justify-between">
                <span className="text-[9px] text-gray-400">
                  Active ({viewsheds.length})
                </span>
                <div className="flex space-x-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={exportViewsheds}
                    className="p-1 h-4 w-4 text-gray-400 hover:text-white"
                    title="Export"
                  >
                    <Download size={8} />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={clearAllViewsheds}
                    className="p-1 h-4 w-4 text-red-400 hover:text-red-300"
                    title="Clear All"
                  >
                    <Trash2 size={8} />
                  </Button>
                </div>
              </div>

              <div className="max-h-20 overflow-y-auto space-y-1">
                {viewsheds.map((viewshed, index) => (
                  <div
                    key={viewshed.id}
                    className={`p-1 rounded text-[9px] ${
                      selectedViewshed === viewshed.id
                        ? 'bg-yellow-900 bg-opacity-50 border border-yellow-600'
                        : 'bg-gray-800 bg-opacity-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">
                        VS{index + 1}
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeViewshed(viewshed.id)}
                        className="p-0.5 h-3 w-3 text-red-400 hover:text-red-300"
                      >
                        <Trash2 size={6} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Instructions */}
          {isActive && viewsheds.length === 0 && (
            <div className="text-[9px] text-gray-500 text-center p-1 border border-gray-600 rounded">
              <Info size={8} className="inline mr-1" />
              Click map to place observers
              <div className="text-[8px] text-green-400 mt-1">
                Using REAL elevation data
              </div>
            </div>
          )}

          {/* Analysis status */}
          {isAnalyzing && (
            <div className="text-[8px] text-blue-400 text-center p-1 border border-blue-600 rounded">
              <CheckCircle size={8} className="inline mr-1" />
              Fetching real elevation data...
            </div>
          )}

          {/* Warning for unavailable service */}
          {serviceStatus?.available === false && (
            <div className="text-[8px] text-red-400 text-center p-1 border border-red-600 rounded">
              <AlertTriangle size={8} className="inline mr-1" />
              Elevation service unavailable
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewshedAnalysis;
