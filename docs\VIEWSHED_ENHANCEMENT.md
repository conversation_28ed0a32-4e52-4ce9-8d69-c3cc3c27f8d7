# Enhanced Viewshed Analysis Implementation

## Overview

The viewshed analysis system has been significantly enhanced to support both simple geometric calculations and advanced terrain-based analysis, similar to the ArcGIS geoprocessing approach you provided.

## Key Enhancements

### 1. Dual Analysis Modes

**Simple Mode (Original)**
- Fast geometric polygon generation
- Simulated terrain variation using random factors
- Suitable for quick analysis and performance-critical scenarios

**Terrain Mode (New)**
- Real elevation data from multiple sources
- True line-of-sight calculations considering terrain obstacles
- Accurate viewshed analysis with actual topographic data

### 2. Elevation Service Integration

**New ElevationService (`src/services/elevationService.ts`)**
- Supports multiple elevation data sources:
  - Mapbox Terrain API
  - Open Elevation API (free alternative)
- Intelligent caching system for performance
- Line-of-sight calculation with obstruction detection
- Elevation profile generation between points

**Key Features:**
```typescript
// Get elevation for a single point
await elevationService.getElevation(lat, lng, 'mapbox');

// Calculate line-of-sight between two points
await elevationService.calculateLineOfSight(
  observerLat, observerLng, observerHeight,
  targetLat, targetLng, targetHeight
);

// Get elevation profile along a path
await elevationService.getElevationProfile(
  startLat, startLng, endLat, endLng, resolution
);
```

### 3. Enhanced Visualization Options

**Binary Visualization**
- Traditional visible/hidden areas
- Single color scheme for simplicity

**Graduated Visualization**
- Visible areas (green)
- Partially visible areas (yellow)
- Hidden areas (red)
- Opacity-based visibility indication

### 4. Advanced Configuration

**New Settings:**
- Analysis Mode: Simple vs Terrain
- Elevation Source: Mapbox vs Open Elevation
- Visualization Mode: Binary vs Graduated
- Resolution: Analysis point density for terrain mode

**Existing Settings Enhanced:**
- Observer height (meters)
- Target height (meters)
- Maximum distance (meters)
- Horizontal/vertical view angles

## Implementation Details

### Enhanced ViewshedAnalysis Component

**Key Functions:**

1. **`generateTerrainViewshed()`**
   - Uses elevation service for real terrain data
   - Calculates visibility for each direction ray
   - Creates detailed feature collection with visibility properties

2. **`generateSimpleViewshed()`**
   - Maintains original geometric approach
   - Backward compatible with existing functionality

3. **`addViewshedToMap()`**
   - Supports both visualization modes
   - Dynamic styling based on visibility properties

### Terrain Analysis Process

1. **Observer Placement**: User clicks map to place viewshed observer
2. **Ray Casting**: System casts rays in all directions from observer
3. **Elevation Sampling**: Elevation service fetches terrain data along each ray
4. **Line-of-Sight Calculation**: Determines visibility considering terrain obstacles
5. **Visualization**: Creates color-coded polygons showing visibility areas

### Performance Optimizations

**Caching Strategy:**
- Elevation data cached for 1 hour
- Coordinate-based cache keys
- Automatic cache cleanup

**Efficient Processing:**
- Configurable resolution (10-100 analysis points)
- Asynchronous elevation data fetching
- Progressive rendering for large viewsheds

## Usage Instructions

### Basic Usage

1. **Access Viewshed Tool**
   - Click viewshed icon in tactical map toolbar
   - Tool appears in top-left drawing tools section

2. **Configure Analysis**
   - Click settings gear icon
   - Choose analysis mode (Simple/Terrain)
   - Adjust parameters as needed

3. **Place Observers**
   - Click "Start" to activate placement mode
   - Click map locations to place viewshed observers
   - Analysis runs automatically for each observer

4. **View Results**
   - Viewshed areas appear on map
   - Color coding indicates visibility levels
   - Observer markers show placement locations

### Advanced Configuration

**Terrain Mode Settings:**
```
Analysis Mode: Terrain
Elevation Source: Open Elevation (free) or Mapbox (requires token)
Visualization: Graduated (recommended for terrain analysis)
Resolution: 50 (balance of accuracy and performance)
Observer Height: 1.7m (typical human eye level)
Max Distance: 1000m (adjust based on terrain and requirements)
```

**Simple Mode Settings:**
```
Analysis Mode: Simple
Visualization: Binary (traditional approach)
Resolution: Not applicable (uses fixed 50 points)
```

## Integration Benefits

### Backward Compatibility
- Existing simple mode functionality preserved
- No breaking changes to existing viewshed usage
- Smooth migration path for users

### UI Consistency
- Maintains tactical map design language
- Compact 160px panel width
- Military-style color scheme and typography
- Auto-hide functionality preserved

### Performance Considerations
- Terrain analysis is more computationally intensive
- Caching reduces repeated elevation API calls
- Configurable resolution allows performance tuning
- Simple mode remains available for fast analysis

## Comparison with ArcGIS Approach

**Similarities:**
- Click-to-place observer functionality
- Real elevation data integration
- Geoprocessing-style analysis
- Visual feedback during calculation

**Enhancements:**
- Dual mode support (simple + terrain)
- Multiple elevation data sources
- Graduated visibility visualization
- Integrated tactical map UI
- Performance optimizations

## Future Enhancements

**Potential Additions:**
1. **Multiple Observer Analysis**: Combine viewsheds from multiple observers
2. **Intervisibility Analysis**: Calculate mutual visibility between points
3. **Elevation Profile Visualization**: Show terrain cross-sections
4. **Custom Elevation Sources**: Support for local DEM files
5. **Viewshed Comparison**: Compare different scenarios side-by-side

## Technical Notes

**Elevation Data Sources:**
- **Mapbox Terrain**: High accuracy, requires API token, rate limited
- **Open Elevation**: Free, moderate accuracy, public API

**Coordinate System:**
- Uses WGS84 (EPSG:4326) for elevation queries
- Automatic conversion for MapLibre display

**Error Handling:**
- Graceful fallback to simple mode if elevation service fails
- Cache invalidation on errors
- User feedback for network issues

This enhanced viewshed system provides professional-grade terrain analysis capabilities while maintaining the tactical map's performance and usability standards.
