import React, { useEffect, useRef } from 'react';
import maplibregl from 'maplibre-gl';
import {
  MapPin,
  Navigation,
  Crosshair,
  Copy,
  Download,
  Ruler,
  Eye,
  Plus,
  Target,
  Zap
} from 'lucide-react';
import Button from '@/components/ui/Button';

interface MapLibreContextMenuProps {
  x: number;
  y: number;
  lngLat: [number, number];
  map: maplibregl.Map;
  onClose: () => void;
}

const MapLibreContextMenu: React.FC<MapLibreContextMenuProps> = ({
  x,
  y,
  lngLat,
  map,
  onClose
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  // Position menu to stay within viewport
  const getMenuPosition = () => {
    const menuWidth = 200;
    const menuHeight = 300;
    const padding = 10;

    let adjustedX = x;
    let adjustedY = y;

    // Adjust horizontal position
    if (x + menuWidth > window.innerWidth - padding) {
      adjustedX = x - menuWidth;
    }

    // Adjust vertical position
    if (y + menuHeight > window.innerHeight - padding) {
      adjustedY = y - menuHeight;
    }

    return {
      left: Math.max(padding, adjustedX),
      top: Math.max(padding, adjustedY)
    };
  };

  const position = getMenuPosition();

  // Make map available globally for cleanup functions
  useEffect(() => {
    (window as any).map = map;
    return () => {
      delete (window as any).map;
    };
  }, [map]);

  // Menu actions
  const handleCenterHere = () => {
    map.flyTo({ center: lngLat, zoom: map.getZoom() });
    onClose();
  };

  const handleZoomIn = () => {
    map.flyTo({ center: lngLat, zoom: map.getZoom() + 1 });
    onClose();
  };

  const handleZoomOut = () => {
    map.flyTo({ center: lngLat, zoom: map.getZoom() - 1 });
    onClose();
  };

  const handleCopyCoordinates = () => {
    const coordText = `${lngLat[1].toFixed(6)}, ${lngLat[0].toFixed(6)}`;
    navigator.clipboard.writeText(coordText).then(() => {
      console.log('Coordinates copied to clipboard:', coordText);
      // TODO: Show toast notification
    }).catch(err => {
      console.error('Failed to copy coordinates:', err);
    });
    onClose();
  };

  const handleAddMarker = () => {
    // Create a temporary marker
    const marker = new maplibregl.Marker({
      color: '#FF4444',
      draggable: true
    })
      .setLngLat(lngLat)
      .addTo(map);

    // Add popup with coordinates
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false
    })
      .setLngLat(lngLat)
      .setHTML(`
        <div class="tactical-popup">
          <h3 class="text-sm font-bold text-white mb-2">WAYPOINT</h3>
          <div class="text-xs text-gray-300">
            <div><strong>LAT:</strong> ${lngLat[1].toFixed(6)}</div>
            <div><strong>LNG:</strong> ${lngLat[0].toFixed(6)}</div>
          </div>
          <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();"
                  class="mt-2 px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
            Remove
          </button>
        </div>
      `)
      .addTo(map);

    onClose();
  };

  const handleMeasureDistance = () => {
    // Start distance measurement from this point
    console.log('Starting distance measurement from:', lngLat);

    // Create a temporary marker to show the start point
    const startMarker = new maplibregl.Marker({
      color: '#00FF00',
      draggable: false
    })
      .setLngLat(lngLat)
      .addTo(map);

    // Add popup to indicate measurement start
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false,
      offset: 25
    })
      .setLngLat(lngLat)
      .setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-green-400">Measurement Start</div>
          <div class="text-gray-300">Click another point to measure distance</div>
          <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();"
                  class="mt-2 px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
            Cancel
          </button>
        </div>
      `)
      .addTo(map);

    // Set up click handler for end point
    const handleEndPoint = (e: maplibregl.MapMouseEvent) => {
      const endLngLat = [e.lngLat.lng, e.lngLat.lat] as [number, number];

      // Calculate distance using Haversine formula
      const distance = calculateDistance(lngLat, endLngLat);

      // Create end marker
      const endMarker = new maplibregl.Marker({
        color: '#FF0000',
        draggable: false
      })
        .setLngLat(endLngLat)
        .addTo(map);

      // Create line between points
      const lineId = `measurement-line-${Date.now()}`;
      map.addSource(lineId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: [lngLat, endLngLat]
          }
        }
      });

      map.addLayer({
        id: lineId,
        type: 'line',
        source: lineId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#00FF00',
          'line-width': 3,
          'line-dasharray': [2, 2]
        }
      });

      // Show distance popup
      const midpoint = [
        (lngLat[0] + endLngLat[0]) / 2,
        (lngLat[1] + endLngLat[1]) / 2
      ] as [number, number];

      new maplibregl.Popup({
        closeButton: true,
        closeOnClick: false
      })
        .setLngLat(midpoint)
        .setHTML(`
          <div class="text-xs">
            <div class="font-semibold text-blue-400">Distance Measurement</div>
            <div class="text-gray-300">${distance.toFixed(2)} km</div>
            <button onclick="
              this.closest('.maplibregl-popup').remove();
              document.querySelectorAll('.maplibregl-marker').forEach(m => m.remove());
              if (window.map && window.map.getLayer('${lineId}')) {
                window.map.removeLayer('${lineId}');
                window.map.removeSource('${lineId}');
              }
            " class="mt-2 px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
              Clear
            </button>
          </div>
        `)
        .addTo(map);

      // Remove click handler
      map.off('click', handleEndPoint);

      // Clean up start popup and marker
      popup.remove();
    };

    // Add click handler for end point
    map.on('click', handleEndPoint);

    onClose();
  };

  // Helper function to calculate distance between two points
  const calculateDistance = (point1: [number, number], point2: [number, number]): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (point2[1] - point1[1]) * Math.PI / 180;
    const dLon = (point2[0] - point1[0]) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1[1] * Math.PI / 180) * Math.cos(point2[1] * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const handleViewshed = () => {
    // Start viewshed analysis from this point
    console.log('Starting viewshed analysis from:', lngLat);

    // Create observer point marker
    const observerMarker = new maplibregl.Marker({
      color: '#0088FF',
      draggable: false
    })
      .setLngLat(lngLat)
      .addTo(map);

    // Add popup to show viewshed parameters
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false,
      offset: 25
    })
      .setLngLat(lngLat)
      .setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-blue-400">Viewshed Analysis</div>
          <div class="text-gray-300 mb-2">Observer Position</div>
          <div class="space-y-1">
            <div>
              <label class="text-gray-400">Range (km):</label>
              <input type="number" id="viewshed-range" value="5" min="1" max="50"
                     class="ml-1 w-12 px-1 bg-gray-700 text-white text-xs rounded">
            </div>
            <div>
              <label class="text-gray-400">Height (m):</label>
              <input type="number" id="viewshed-height" value="10" min="1" max="100"
                     class="ml-1 w-12 px-1 bg-gray-700 text-white text-xs rounded">
            </div>
          </div>
          <div class="mt-2 space-x-1">
            <button onclick="calculateViewshed()"
                    class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
              Calculate
            </button>
            <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();"
                    class="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700">
              Cancel
            </button>
          </div>
        </div>
      `)
      .addTo(map);

    // Add global function for viewshed calculation
    (window as any).calculateViewshed = () => {
      const range = parseFloat((document.getElementById('viewshed-range') as HTMLInputElement)?.value || '5');
      const height = parseFloat((document.getElementById('viewshed-height') as HTMLInputElement)?.value || '10');

      // Generate viewshed polygon (simplified circular approximation)
      const viewshedId = `viewshed-${Date.now()}`;
      const center = lngLat;
      const radiusInDegrees = range / 111; // Rough conversion km to degrees

      // Create circular viewshed polygon
      const points = [];
      for (let i = 0; i <= 360; i += 10) {
        const angle = i * Math.PI / 180;
        const lat = center[1] + radiusInDegrees * Math.cos(angle);
        const lng = center[0] + radiusInDegrees * Math.sin(angle) / Math.cos(center[1] * Math.PI / 180);
        points.push([lng, lat]);
      }

      // Add viewshed layer
      map.addSource(viewshedId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {
            range: range,
            height: height
          },
          geometry: {
            type: 'Polygon',
            coordinates: [points]
          }
        }
      });

      map.addLayer({
        id: viewshedId,
        type: 'fill',
        source: viewshedId,
        paint: {
          'fill-color': '#0088FF',
          'fill-opacity': 0.2
        }
      });

      map.addLayer({
        id: `${viewshedId}-outline`,
        type: 'line',
        source: viewshedId,
        paint: {
          'line-color': '#0088FF',
          'line-width': 2,
          'line-dasharray': [3, 3]
        }
      });

      // Update popup to show results
      popup.setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-blue-400">Viewshed Analysis</div>
          <div class="text-gray-300">Analysis Complete</div>
          <div class="mt-1 space-y-1">
            <div>Range: ${range} km</div>
            <div>Observer Height: ${height} m</div>
            <div class="text-yellow-400">Note: Simplified analysis</div>
          </div>
          <button onclick="
            this.closest('.maplibregl-popup').remove();
            this.closest('.maplibregl-marker').remove();
            if (window.map && window.map.getLayer('${viewshedId}')) {
              window.map.removeLayer('${viewshedId}');
              window.map.removeLayer('${viewshedId}-outline');
              window.map.removeSource('${viewshedId}');
            }
          " class="mt-2 px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
            Clear
          </button>
        </div>
      `);
    };

    onClose();
  };

  const handleAddIncident = () => {
    // Create incident creation form at this location
    console.log('Creating incident at:', lngLat);

    // Create marker for new incident
    const incidentMarker = new maplibregl.Marker({
      color: '#FF4444',
      draggable: true
    })
      .setLngLat(lngLat)
      .addTo(map);

    // Add popup with incident creation form
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false,
      offset: 25
    })
      .setLngLat(lngLat)
      .setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-red-400">New Incident</div>
          <div class="text-gray-300 mb-2">LAT: ${lngLat[1].toFixed(6)}, LNG: ${lngLat[0].toFixed(6)}</div>
          <div class="space-y-2">
            <div>
              <label class="text-gray-400 block">Type:</label>
              <select id="incident-type" class="w-full px-1 bg-gray-700 text-white text-xs rounded">
                <option value="emergency">Emergency</option>
                <option value="fire">Fire</option>
                <option value="medical">Medical</option>
                <option value="security">Security</option>
                <option value="accident">Accident</option>
                <option value="natural_disaster">Natural Disaster</option>
              </select>
            </div>
            <div>
              <label class="text-gray-400 block">Severity:</label>
              <select id="incident-severity" class="w-full px-1 bg-gray-700 text-white text-xs rounded">
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="CRITICAL">Critical</option>
              </select>
            </div>
            <div>
              <label class="text-gray-400 block">Description:</label>
              <textarea id="incident-description" placeholder="Brief description..."
                        class="w-full px-1 bg-gray-700 text-white text-xs rounded h-12 resize-none"></textarea>
            </div>
          </div>
          <div class="mt-2 space-x-1">
            <button onclick="createIncident()"
                    class="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
              Create
            </button>
            <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();"
                    class="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700">
              Cancel
            </button>
          </div>
        </div>
      `)
      .addTo(map);

    // Add global function for incident creation
    (window as any).createIncident = () => {
      const type = (document.getElementById('incident-type') as HTMLSelectElement)?.value || 'emergency';
      const severity = (document.getElementById('incident-severity') as HTMLSelectElement)?.value || 'MEDIUM';
      const description = (document.getElementById('incident-description') as HTMLTextAreaElement)?.value || 'New incident';

      // Get current marker position (in case it was dragged)
      const currentPos = incidentMarker.getLngLat();

      console.log('Creating incident:', {
        type,
        severity,
        description,
        coordinates: [currentPos.lng, currentPos.lat],
        timestamp: new Date().toISOString()
      });

      // Update popup to show success
      popup.setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-green-400">Incident Created</div>
          <div class="text-gray-300">Type: ${type}</div>
          <div class="text-gray-300">Severity: ${severity}</div>
          <div class="text-gray-300">Location: ${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)}</div>
          <button onclick="this.closest('.maplibregl-popup').remove();"
                  class="mt-2 px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
            Close
          </button>
        </div>
      `);

      // TODO: In a real implementation, this would save to the incident store
      // For now, just keep the marker on the map as a visual indicator
    };

    onClose();
  };

  const handleAddResponse = () => {
    // Create response creation form at this location
    console.log('Creating response at:', lngLat);

    // Create marker for new response
    const responseMarker = new maplibregl.Marker({
      color: '#00AA00',
      draggable: true
    })
      .setLngLat(lngLat)
      .addTo(map);

    // Add popup with response creation form
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false,
      offset: 25
    })
      .setLngLat(lngLat)
      .setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-green-400">New Response</div>
          <div class="text-gray-300 mb-2">LAT: ${lngLat[1].toFixed(6)}, LNG: ${lngLat[0].toFixed(6)}</div>
          <div class="space-y-2">
            <div>
              <label class="text-gray-400 block">Type:</label>
              <select id="response-type" class="w-full px-1 bg-gray-700 text-white text-xs rounded">
                <option value="police">Police</option>
                <option value="fire_department">Fire Department</option>
                <option value="ambulance">Ambulance</option>
                <option value="rescue_team">Rescue Team</option>
                <option value="military">Military</option>
                <option value="support">Support</option>
              </select>
            </div>
            <div>
              <label class="text-gray-400 block">Status:</label>
              <select id="response-status" class="w-full px-1 bg-gray-700 text-white text-xs rounded">
                <option value="PLANNED">Planned</option>
                <option value="ACTIVE">Active</option>
                <option value="IN_PROGRESS">In Progress</option>
              </select>
            </div>
            <div>
              <label class="text-gray-400 block">Personnel:</label>
              <input type="number" id="response-personnel" value="1" min="1" max="100"
                     class="w-full px-1 bg-gray-700 text-white text-xs rounded">
            </div>
            <div>
              <label class="text-gray-400 block">Notes:</label>
              <textarea id="response-notes" placeholder="Response details..."
                        class="w-full px-1 bg-gray-700 text-white text-xs rounded h-12 resize-none"></textarea>
            </div>
          </div>
          <div class="mt-2 space-x-1">
            <button onclick="createResponse()"
                    class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
              Create
            </button>
            <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();"
                    class="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700">
              Cancel
            </button>
          </div>
        </div>
      `)
      .addTo(map);

    // Add global function for response creation
    (window as any).createResponse = () => {
      const type = (document.getElementById('response-type') as HTMLSelectElement)?.value || 'police';
      const status = (document.getElementById('response-status') as HTMLSelectElement)?.value || 'PLANNED';
      const personnel = parseInt((document.getElementById('response-personnel') as HTMLInputElement)?.value || '1');
      const notes = (document.getElementById('response-notes') as HTMLTextAreaElement)?.value || 'New response';

      // Get current marker position (in case it was dragged)
      const currentPos = responseMarker.getLngLat();

      console.log('Creating response:', {
        type,
        status,
        personnel,
        notes,
        coordinates: [currentPos.lng, currentPos.lat],
        timestamp: new Date().toISOString()
      });

      // Update popup to show success
      popup.setHTML(`
        <div class="text-xs">
          <div class="font-semibold text-green-400">Response Created</div>
          <div class="text-gray-300">Type: ${type}</div>
          <div class="text-gray-300">Status: ${status}</div>
          <div class="text-gray-300">Personnel: ${personnel}</div>
          <div class="text-gray-300">Location: ${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)}</div>
          <button onclick="this.closest('.maplibregl-popup').remove();"
                  class="mt-2 px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
            Close
          </button>
        </div>
      `);

      // TODO: In a real implementation, this would save to the response store
      // For now, just keep the marker on the map as a visual indicator
    };

    onClose();
  };

  const menuItems = [
    {
      label: 'Center Here',
      icon: <Crosshair size={16} />,
      action: handleCenterHere
    },
    {
      label: 'Zoom In',
      icon: <Plus size={16} />,
      action: handleZoomIn
    },
    {
      label: 'Zoom Out',
      icon: <Target size={16} />,
      action: handleZoomOut
    },
    { type: 'separator' },
    {
      label: 'Copy Coordinates',
      icon: <Copy size={16} />,
      action: handleCopyCoordinates
    },
    {
      label: 'Add Marker',
      icon: <MapPin size={16} />,
      action: handleAddMarker
    },
    { type: 'separator' },
    {
      label: 'Measure Distance',
      icon: <Ruler size={16} />,
      action: handleMeasureDistance
    },
    {
      label: 'Viewshed Analysis',
      icon: <Eye size={16} />,
      action: handleViewshed
    },
    { type: 'separator' },
    {
      label: 'Add Incident',
      icon: <Zap size={16} />,
      action: handleAddIncident,
      className: 'text-red-400 hover:text-red-300'
    },
    {
      label: 'Add Response',
      icon: <Navigation size={16} />,
      action: handleAddResponse,
      className: 'text-green-400 hover:text-green-300'
    }
  ];

  return (
    <div
      ref={menuRef}
      className="fixed z-[600] bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm min-w-[180px]"
      style={{
        left: position.left,
        top: position.top
      }}
    >
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-600">
        <div className="text-xs font-mono text-gray-300">
          <div><strong>LAT:</strong> {lngLat[1].toFixed(6)}</div>
          <div><strong>LNG:</strong> {lngLat[0].toFixed(6)}</div>
        </div>
      </div>

      {/* Menu items */}
      <div className="py-1">
        {menuItems.map((item, index) => {
          if (item.type === 'separator') {
            return (
              <div
                key={index}
                className="h-px bg-gray-600 my-1 mx-2"
              />
            );
          }

          return (
            <button
              key={index}
              onClick={item.action}
              className={`
                w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 hover:text-white
                flex items-center space-x-2 transition-colors duration-150
                ${item.className || ''}
              `}
            >
              {item.icon}
              <span>{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default MapLibreContextMenu;
