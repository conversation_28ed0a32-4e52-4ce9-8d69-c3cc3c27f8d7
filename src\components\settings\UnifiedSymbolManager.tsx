import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  X,
  Search,
  Plus,
  Download,
  Upload,
  Shield,
  Target,
  MapPin,
  Layers,
  Filter,
  Grid,
  List,
  Star,
  Copy,
  Trash2,
  Edit3,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight,
  Save,
  Refresh<PERSON>w,
  Check,
  <PERSON><PERSON><PERSON>riangle,
  Pa<PERSON>
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { IncidentType, ActionType } from '@/types/incident';
import {
  tacticalSymbols,
  responseSymbols,
  enhancedMilitarySymbols,
  SymbolConfig,
  allTacticalSymbols,
  getIncidentSymbol,
  getResponseSymbol
} from '@/components/map/TacticalSymbols';

// Extended interface combining both SymbolManager and SymbologyEditor functionality
export interface MilitarySymbol extends SymbolConfig {
  id: string;
  name: string;
  category: string;
  subcategory?: string;
  natoCode?: string;
  isCustom: boolean;
  tags: string[];
  size?: number;
  opacity?: number;
  incidentType?: IncidentType;
  responseType?: ActionType;
}

export interface SymbolCategory {
  id: string;
  name: string;
  description: string;
  symbols: MilitarySymbol[];
  expanded: boolean;
}

interface SymbologyState {
  incidents: Record<string, SymbolConfig>;
  responses: Record<string, SymbolConfig>;
  military: Record<string, SymbolConfig>;
}

export interface UnifiedSymbolManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSymbolSelect?: (symbol: MilitarySymbol) => void;
  onPlaceOnMap?: (symbol: MilitarySymbol, coordinates?: [number, number]) => void;
  className?: string;
  mode?: 'manager' | 'editor'; // Default mode
}

// Convert existing tactical symbols to MilitarySymbol format
const convertTacticalSymbolsToMilitary = (): SymbolCategory[] => {
  const categories: SymbolCategory[] = [];

  // Incident symbols category
  const incidentSymbols: MilitarySymbol[] = Object.entries(tacticalSymbols).map(([key, config]) => ({
    id: `incident-${key}`,
    name: config.description,
    category: 'incidents',
    subcategory: 'tactical',
    isCustom: false,
    tags: ['incident', 'tactical', key.replace('_', ' ')],
    size: 32,
    opacity: 0.9,
    incidentType: key as IncidentType,
    ...config
  }));

  categories.push({
    id: 'incidents',
    name: 'Incident Types',
    description: 'Tactical incident symbols for operational planning',
    expanded: true,
    symbols: incidentSymbols
  });

  // Response symbols category
  const responseSymbols_: MilitarySymbol[] = Object.entries(responseSymbols).map(([key, config]) => ({
    id: `response-${key}`,
    name: config.description,
    category: 'responses',
    subcategory: 'operational',
    isCustom: false,
    tags: ['response', 'operational', key.replace('_', ' ')],
    size: 28,
    opacity: 0.9,
    responseType: key as ActionType,
    ...config
  }));

  categories.push({
    id: 'responses',
    name: 'Response Operations',
    description: 'Response and action symbols for tactical operations',
    expanded: false,
    symbols: responseSymbols_
  });

  // Enhanced military symbols category
  const militarySymbols: MilitarySymbol[] = Object.entries(enhancedMilitarySymbols).map(([key, config]) => ({
    id: `military-${key}`,
    name: config.description,
    category: 'military',
    subcategory: 'strategic',
    isCustom: false,
    tags: ['military', 'strategic', key.replace('_', ' ')],
    size: 30,
    opacity: 0.9,
    ...config
  }));

  categories.push({
    id: 'military',
    name: 'Military Assets',
    description: 'Command, control, logistics, and support facilities',
    expanded: false,
    symbols: militarySymbols
  });

  return categories;
};

// Initialize symbol categories from existing tactical symbols
const SYMBOL_CATEGORIES: SymbolCategory[] = convertTacticalSymbolsToMilitary();

const UnifiedSymbolManager: React.FC<UnifiedSymbolManagerProps> = ({
  isOpen,
  onClose,
  onSymbolSelect,
  onPlaceOnMap,
  className = '',
  mode = 'manager'
}) => {
  // Manager mode states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [categories, setCategories] = useState<SymbolCategory[]>(SYMBOL_CATEGORIES);
  const [selectedSymbol, setSelectedSymbol] = useState<MilitarySymbol | null>(null);
  const [customSymbols, setCustomSymbols] = useState<MilitarySymbol[]>([]);

  // Editor mode states
  const [currentMode, setCurrentMode] = useState<'manager' | 'editor'>(mode);
  const [activeTab, setActiveTab] = useState<'incidents' | 'responses' | 'military'>('incidents');
  const [symbology, setSymbology] = useState<SymbologyState>({
    incidents: { ...tacticalSymbols },
    responses: { ...responseSymbols },
    military: { ...enhancedMilitarySymbols }
  });
  const [selectedSymbolKey, setSelectedSymbolKey] = useState<string | null>(null);
  const [editedSymbol, setEditedSymbol] = useState<SymbolConfig | null>(null);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  // Load symbology from localStorage on component mount
  useEffect(() => {
    const savedSymbology = localStorage.getItem('symbology');
    if (savedSymbology) {
      try {
        const parsed = JSON.parse(savedSymbology);

        // Ensure backward compatibility - add military symbols if missing
        const completeSymbology = {
          incidents: parsed.incidents || { ...tacticalSymbols },
          responses: parsed.responses || { ...responseSymbols },
          military: parsed.military || { ...enhancedMilitarySymbols }
        };

        setSymbology(completeSymbology);

        // Save the updated symbology back to localStorage if military was missing
        if (!parsed.military) {
          saveSymbologyToLocalStorage(completeSymbology);
        }
      } catch (error) {
        console.error('Failed to parse saved symbology:', error);
      }
    }
  }, []);

  // Filter symbols based on search and category (Manager mode)
  const filteredSymbols = useMemo(() => {
    let allSymbols: MilitarySymbol[] = [];

    // Collect all symbols from categories
    categories.forEach(category => {
      allSymbols = [...allSymbols, ...category.symbols];
    });

    // Add custom symbols
    allSymbols = [...allSymbols, ...customSymbols];

    // Apply filters
    return allSymbols.filter(symbol => {
      const matchesSearch = searchTerm === '' ||
        symbol.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symbol.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symbol.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || symbol.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [categories, customSymbols, searchTerm, selectedCategory]);

  // Helper functions for Editor mode
  const saveSymbologyToLocalStorage = (data: SymbologyState) => {
    try {
      localStorage.setItem('symbology', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save symbology to localStorage:', error);
    }
  };

  const getCurrentSymbolCollection = () => {
    switch (activeTab) {
      case 'incidents':
        return symbology.incidents;
      case 'responses':
        return symbology.responses;
      case 'military':
        return symbology.military;
      default:
        return symbology.incidents;
    }
  };

  const getTabDisplayName = () => {
    switch (activeTab) {
      case 'incidents':
        return 'Incident';
      case 'responses':
        return 'Response';
      case 'military':
        return 'Military Asset';
      default:
        return 'Symbol';
    }
  };

  // Editor mode handlers
  const handleSaveSymbol = () => {
    if (!selectedSymbolKey || !editedSymbol) return;

    const newSymbology = { ...symbology };

    switch (activeTab) {
      case 'incidents':
        newSymbology.incidents = { ...newSymbology.incidents, [selectedSymbolKey]: editedSymbol };
        break;
      case 'responses':
        newSymbology.responses = { ...newSymbology.responses, [selectedSymbolKey]: editedSymbol };
        break;
      case 'military':
        newSymbology.military = { ...newSymbology.military, [selectedSymbolKey]: editedSymbol };
        break;
    }

    setSymbology(newSymbology);
    saveSymbologyToLocalStorage(newSymbology);

    setResult({
      success: true,
      message: `Symbol ${editedSymbol.description} updated successfully.`
    });

    // Clear result message after 3 seconds
    setTimeout(() => setResult(null), 3000);
  };

  const handleExportSymbology = () => {
    try {
      const dataStr = JSON.stringify(symbology, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

      const exportFileDefaultName = `symbology_export_${new Date().toISOString().slice(0, 10)}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();

      setResult({
        success: true,
        message: 'Symbology exported successfully.'
      });

      // Clear result message after 3 seconds
      setTimeout(() => setResult(null), 3000);
    } catch (error) {
      console.error('Failed to export symbology:', error);
      setResult({
        success: false,
        message: `Failed to export symbology: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  const handleImportSymbology = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const importedSymbology = JSON.parse(content);

        // Validate the imported data - ensure all required categories exist
        if (!importedSymbology.incidents || !importedSymbology.responses) {
          throw new Error('Invalid symbology file format - missing incidents or responses');
        }

        // Add military symbols if missing (for backward compatibility)
        if (!importedSymbology.military) {
          importedSymbology.military = { ...enhancedMilitarySymbols };
        }

        setSymbology(importedSymbology);
        saveSymbologyToLocalStorage(importedSymbology);

        setResult({
          success: true,
          message: 'Symbology imported successfully.'
        });
      } catch (error) {
        console.error('Failed to import symbology:', error);
        setResult({
          success: false,
          message: `Failed to import symbology: ${error instanceof Error ? error.message : String(error)}`
        });
      }

      // Reset file input
      event.target.value = '';
    };

    reader.onerror = () => {
      setResult({
        success: false,
        message: 'Failed to read the file'
      });
      // Reset file input
      event.target.value = '';
    };

    reader.readAsText(file);
  };

  const handleResetSymbology = () => {
    const defaultSymbology = {
      incidents: { ...tacticalSymbols },
      responses: { ...responseSymbols },
      military: { ...enhancedMilitarySymbols }
    };

    setSymbology(defaultSymbology);
    saveSymbologyToLocalStorage(defaultSymbology);
    setSelectedSymbolKey(null);
    setEditedSymbol(null);

    setResult({
      success: true,
      message: 'Symbology reset to defaults.'
    });

    // Clear result message after 3 seconds
    setTimeout(() => setResult(null), 3000);
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${className}`}>
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-75 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative w-[95vw] h-[90vh] bg-gray-900 border border-gray-600 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-600 bg-gray-800">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-military-heading text-military-white uppercase">
              {currentMode === 'manager' ? 'SYMBOL MANAGER' : 'SYMBOLOGY EDITOR'}
            </h2>

            {/* Mode Toggle */}
            <div className="flex border border-military-border rounded">
              <Button
                size="sm"
                variant={currentMode === 'manager' ? 'default' : 'ghost'}
                onClick={() => setCurrentMode('manager')}
                className="rounded-r-none text-xs"
              >
                <Layers size={14} className="mr-1" />
                MANAGER
              </Button>
              <Button
                size="sm"
                variant={currentMode === 'editor' ? 'default' : 'ghost'}
                onClick={() => setCurrentMode('editor')}
                className="rounded-l-none text-xs"
              >
                <Edit3 size={14} className="mr-1" />
                EDITOR
              </Button>
            </div>
          </div>

          <Button
            size="sm"
            variant="ghost"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={20} />
          </Button>
        </div>

        {/* Content Area */}
        <div className="h-[calc(90vh-5rem)] overflow-hidden">
          {currentMode === 'manager' ? (
            <div className="flex h-full">
              {/* Sidebar - Categories and Filters */}
              <div className="w-80 border-r border-gray-600 bg-gray-850 bg-opacity-50 overflow-y-auto">
                <div className="p-4">
                  {/* Search */}
                  <div className="relative mb-4">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search symbols..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm focus:border-blue-500 focus:outline-none"
                    />
                  </div>

                  {/* Category Filter */}
                  <div className="mb-4">
                    <label className="block text-xs font-mono font-bold text-gray-400 uppercase tracking-wider mb-2">
                      Category Filter
                    </label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full bg-gray-800 border border-gray-600 rounded text-white text-sm p-2 focus:border-blue-500 focus:outline-none"
                    >
                      <option value="all">All Categories</option>
                      <option value="incidents">Incident Types</option>
                      <option value="responses">Response Operations</option>
                      <option value="military">Military Assets</option>
                    </select>
                  </div>

                  {/* View Mode Toggle */}
                  <div className="mb-4">
                    <label className="block text-xs font-mono font-bold text-gray-400 uppercase tracking-wider mb-2">
                      View Mode
                    </label>
                    <div className="flex border border-gray-600 rounded">
                      <Button
                        size="sm"
                        variant={viewMode === 'grid' ? 'default' : 'ghost'}
                        onClick={() => setViewMode('grid')}
                        className="flex-1 rounded-r-none text-xs"
                      >
                        <Grid size={14} />
                      </Button>
                      <Button
                        size="sm"
                        variant={viewMode === 'list' ? 'default' : 'ghost'}
                        onClick={() => setViewMode('list')}
                        className="flex-1 rounded-l-none text-xs"
                      >
                        <List size={14} />
                      </Button>
                    </div>
                  </div>

                  {/* Categories */}
                  <div className="space-y-2">
                    <label className="block text-xs font-mono font-bold text-gray-400 uppercase tracking-wider mb-2">
                      Symbol Categories
                    </label>
                    {categories.map(category => (
                      <div key={category.id} className="border border-gray-700 rounded">
                        <button
                          onClick={() => {
                            const newCategories = categories.map(cat =>
                              cat.id === category.id ? { ...cat, expanded: !cat.expanded } : cat
                            );
                            setCategories(newCategories);
                          }}
                          className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-800 transition-colors"
                        >
                          <div>
                            <div className="text-sm font-medium text-white">{category.name}</div>
                            <div className="text-xs text-gray-400">{category.symbols.length} symbols</div>
                          </div>
                          {category.expanded ? (
                            <ChevronDown size={16} className="text-gray-400" />
                          ) : (
                            <ChevronRight size={16} className="text-gray-400" />
                          )}
                        </button>

                        {category.expanded && (
                          <div className="border-t border-gray-700 p-2 space-y-1">
                            {category.symbols.slice(0, 5).map(symbol => (
                              <div
                                key={symbol.id}
                                className="flex items-center space-x-2 p-1 text-xs text-gray-300 hover:bg-gray-800 rounded cursor-pointer"
                                onClick={() => setSelectedSymbol(symbol)}
                              >
                                <div
                                  className="w-4 h-4 rounded flex items-center justify-center text-[8px]"
                                  style={{ backgroundColor: symbol.color }}
                                >
                                  {symbol.text || symbol.symbol}
                                </div>
                                <span className="truncate">{symbol.name}</span>
                              </div>
                            ))}
                            {category.symbols.length > 5 && (
                              <div className="text-xs text-gray-500 pl-6">
                                +{category.symbols.length - 5} more...
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Main Content Area */}
              <div className="flex-1 flex flex-col">
                {/* Symbols Grid/List */}
                <div className="flex-1 p-4 overflow-y-auto">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-mono font-bold text-gray-300 uppercase tracking-wider">
                      Available Symbols ({filteredSymbols.length})
                    </h3>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        onClick={() => {/* TODO: Add custom symbol creation */}}
                        className="flex items-center space-x-1"
                      >
                        <Plus size={14} />
                        <span>Create Custom</span>
                      </Button>
                    </div>
                  </div>

                  {/* Symbols Display */}
                  <div className={viewMode === 'grid'
                    ? "grid grid-cols-4 gap-4"
                    : "space-y-2"
                  }>
                    {filteredSymbols.map(symbol => (
                      <div
                        key={symbol.id}
                        className={`border border-gray-600 rounded p-3 hover:border-blue-500 cursor-pointer transition-colors ${
                          selectedSymbol?.id === symbol.id ? 'border-blue-500 bg-blue-500 bg-opacity-10' : ''
                        }`}
                        onClick={() => setSelectedSymbol(symbol)}
                      >
                        {viewMode === 'grid' ? (
                          <div className="text-center">
                            <div
                              className="w-12 h-12 mx-auto mb-2 rounded flex items-center justify-center text-lg"
                              style={{ backgroundColor: symbol.color }}
                            >
                              {symbol.text || symbol.symbol}
                            </div>
                            <div className="text-xs text-white font-medium truncate">{symbol.name}</div>
                            <div className="text-xs text-gray-400 capitalize">{symbol.category}</div>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-3">
                            <div
                              className="w-8 h-8 rounded flex items-center justify-center text-sm"
                              style={{ backgroundColor: symbol.color }}
                            >
                              {symbol.text || symbol.symbol}
                            </div>
                            <div className="flex-1">
                              <div className="text-sm text-white font-medium">{symbol.name}</div>
                              <div className="text-xs text-gray-400">{symbol.description}</div>
                            </div>
                            <div className="text-xs text-gray-500 capitalize">{symbol.category}</div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Symbol Details Panel */}
                {selectedSymbol && (
                  <div className="border-t border-gray-600 p-4 bg-gray-800 bg-opacity-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-white mb-2">{selectedSymbol.name}</h4>
                        <p className="text-sm text-gray-300 mb-3">{selectedSymbol.description}</p>

                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="text-gray-400">Category:</span>
                            <span className="text-white ml-2 capitalize">{selectedSymbol.category}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">Type:</span>
                            <span className="text-white ml-2">{selectedSymbol.subcategory || 'Standard'}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">Color:</span>
                            <span className="text-white ml-2">{selectedSymbol.color}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">Shape:</span>
                            <span className="text-white ml-2 capitalize">{selectedSymbol.shape || 'Circle'}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col space-y-2 ml-4">
                        <Button
                          size="sm"
                          onClick={() => onPlaceOnMap?.(selectedSymbol)}
                          className="flex items-center space-x-1"
                        >
                          <MapPin size={14} />
                          <span>Place on Map</span>
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {/* TODO: Copy symbol */}}
                          className="flex items-center space-x-1"
                        >
                          <Copy size={14} />
                          <span>Copy</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full">
              {/* Editor Header */}
              <div className="p-4 border-b border-gray-600">
                <p className="text-military-white font-military-body mb-4">
                  Customize the symbols used on the map for incidents, responses, and military assets. Full NATO MIL-STD-2525 compatibility.
                </p>

                {/* NATO Compatibility Info for Military Tab */}
                {activeTab === 'military' && (
                  <div className="mb-4 p-3 border border-military-accent bg-military-accent bg-opacity-10">
                    <p className="text-military-white font-military-body text-sm">
                      <Shield size={14} className="inline mr-2" />
                      <strong>NATO MIL-STD-2525 Compatible:</strong> Military asset symbols follow NATO standardization
                      for interoperability with allied forces and tactical systems.
                    </p>
                  </div>
                )}

                {/* Tab selector */}
                <div className="flex mb-4">
                  <Button
                    variant="military"
                    className={`military-btn ${activeTab === 'incidents' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
                    onClick={() => setActiveTab('incidents')}
                  >
                    INCIDENT SYMBOLS
                  </Button>
                  <Button
                    variant="military"
                    className={`military-btn ml-2 ${activeTab === 'responses' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
                    onClick={() => setActiveTab('responses')}
                  >
                    RESPONSE SYMBOLS
                  </Button>
                  <Button
                    variant="military"
                    className={`military-btn ml-2 ${activeTab === 'military' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
                    onClick={() => setActiveTab('military')}
                  >
                    <Shield size={14} className="mr-1" />
                    MILITARY ASSETS
                  </Button>
                </div>

                {/* Import/Export/Reset buttons */}
                <div className="mb-4 flex">
                  <Button
                    variant="military"
                    className="military-btn bg-military-blue"
                    leftIcon={<Download size={16} />}
                    onClick={handleExportSymbology}
                  >
                    EXPORT SYMBOLOGY
                  </Button>

                  <label className="ml-2">
                    <Button
                      variant="military"
                      className="military-btn bg-military-blue"
                      leftIcon={<Upload size={16} />}
                      onClick={() => document.getElementById('symbology-import')?.click()}
                    >
                      IMPORT SYMBOLOGY
                    </Button>
                    <input
                      id="symbology-import"
                      type="file"
                      accept=".json"
                      onChange={handleImportSymbology}
                      className="hidden"
                    />
                  </label>

                  <Button
                    variant="military"
                    className="military-btn bg-military-red ml-2"
                    leftIcon={<RefreshCw size={16} />}
                    onClick={handleResetSymbology}
                  >
                    RESET TO DEFAULTS
                  </Button>
                </div>
              </div>

              {/* Symbol list and editor */}
              <div className="flex flex-1 border-t border-military-border">
                {/* Symbol list */}
                <div className="w-1/2 border-r border-military-border overflow-y-auto">
                  <div className="p-2 bg-military-navy border-b border-military-border">
                    <h4 className="text-sm font-military-heading text-military-white uppercase">
                      {activeTab === 'incidents' && 'INCIDENT SYMBOLS'}
                      {activeTab === 'responses' && 'RESPONSE SYMBOLS'}
                      {activeTab === 'military' && 'MILITARY ASSET SYMBOLS'}
                    </h4>
                  </div>
                  <div className="divide-y divide-military-border">
                    {Object.entries(getCurrentSymbolCollection()).map(([key, config]) => (
                      <div
                        key={key}
                        className={`p-2 border ${selectedSymbolKey === key ? 'border-military-accent' : 'border-military-border'} cursor-pointer hover:bg-military-navy`}
                        onClick={() => {
                          setSelectedSymbolKey(key);
                          setEditedSymbol({ ...config });
                        }}
                      >
                        <div className="flex items-center">
                          <div
                            className="w-8 h-8 rounded flex items-center justify-center text-sm mr-2"
                            style={{ backgroundColor: config.color }}
                          >
                            {config.text || config.symbol}
                          </div>
                          <div className="ml-2">
                            <div className="text-sm text-military-white">{config.description}</div>
                            <div className="text-xs text-military-white opacity-70">{key}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Symbol editor */}
                <div className="w-1/2 p-4">
                  {selectedSymbolKey && editedSymbol ? (
                    <div>
                      <h4 className="text-md font-military-heading text-military-white uppercase mb-4">
                        EDIT {getTabDisplayName().toUpperCase()} SYMBOL
                      </h4>

                      <div className="mb-4">
                        <label className="block text-sm font-military-body text-military-white uppercase mb-1">Symbol Type</label>
                        <input
                          type="text"
                          value={selectedSymbolKey}
                          disabled
                          className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 opacity-50"
                        />
                      </div>

                      <div className="mb-4">
                        <label className="block text-sm font-military-body text-military-white uppercase mb-1">Description</label>
                        <input
                          type="text"
                          value={editedSymbol.description}
                          onChange={(e) => setEditedSymbol({ ...editedSymbol, description: e.target.value })}
                          className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2"
                        />
                      </div>

                      <div className="mb-4">
                        <label className="block text-sm font-military-body text-military-white uppercase mb-1">Shape</label>
                        <select
                          value={editedSymbol.shape || 'circle'}
                          onChange={(e) => setEditedSymbol({ ...editedSymbol, shape: e.target.value })}
                          className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2"
                        >
                          <option value="circle">Circle</option>
                          <option value="diamond">Diamond</option>
                          <option value="square">Square</option>
                          <option value="triangle">Triangle</option>
                          <option value="hexagon">Hexagon</option>
                          <option value="star">Star</option>
                        </select>
                      </div>

                      <div className="mb-4">
                        <label className="block text-sm font-military-body text-military-white uppercase mb-1">Symbol Text</label>
                        <input
                          type="text"
                          value={editedSymbol.text || ''}
                          onChange={(e) => setEditedSymbol({ ...editedSymbol, text: e.target.value })}
                          placeholder={
                            activeTab === 'military'
                              ? "Enter NATO-compatible text (e.g., CP, OP, SUP)"
                              : "Enter text to display inside symbol (e.g., ADO, IBO)"
                          }
                          className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2"
                        />
                        <p className="text-xs text-military-white opacity-70 mt-1">
                          {activeTab === 'military'
                            ? "NATO MIL-STD-2525 compatible abbreviations recommended. Keep text short (1-4 characters)."
                            : "Leave empty for no text. Keep text short (1-4 characters) for best results."
                          }
                        </p>
                      </div>

                      <div className="mb-4">
                        <label className="block text-sm font-military-body text-military-white uppercase mb-1">Color (Hex)</label>
                        <div className="flex items-center">
                          <input
                            type="text"
                            value={editedSymbol.color}
                            onChange={(e) => setEditedSymbol({ ...editedSymbol, color: e.target.value })}
                            className="flex-1 bg-military-navy border border-military-border text-military-white font-military-body p-2"
                          />
                          <input
                            type="color"
                            value={editedSymbol.color}
                            onChange={(e) => setEditedSymbol({ ...editedSymbol, color: e.target.value })}
                            className="ml-2 h-8 w-8 border border-military-border cursor-pointer"
                          />
                        </div>
                      </div>

                      <div className="mb-4">
                        <label className="block text-sm font-military-body text-military-white uppercase mb-1">Preview</label>
                        <div className="p-4 border border-military-border flex justify-center">
                          <div
                            className="w-8 h-8 rounded flex items-center justify-center text-sm"
                            style={{ backgroundColor: editedSymbol.color }}
                          >
                            {editedSymbol.text || editedSymbol.symbol}
                          </div>
                        </div>
                      </div>

                      <Button
                        variant="military"
                        className="military-btn bg-military-darkgreen"
                        leftIcon={<Save size={16} />}
                        onClick={handleSaveSymbol}
                      >
                        SAVE CHANGES
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full">
                      <Eye size={48} className="text-military-white opacity-30 mb-4" />
                      <p className="text-military-white font-military-body text-center">
                        Select a symbol from the list to edit its properties.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Result message */}
              {result && (
                <div className={`p-3 border-t ${result.success ? 'border-military-darkgreen bg-military-darkgreen bg-opacity-20' : 'border-military-red bg-military-red bg-opacity-20'}`}>
                  <p className="text-military-white font-military-body flex items-start">
                    {result.success ? (
                      <Check size={16} className="text-military-green mr-2 mt-1 flex-shrink-0" />
                    ) : (
                      <AlertTriangle size={16} className="text-military-amber mr-2 mt-1 flex-shrink-0" />
                    )}
                    {result.message}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnifiedSymbolManager;
