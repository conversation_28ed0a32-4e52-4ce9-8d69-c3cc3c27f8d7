import React, { useEffect, useRef, useState } from 'react';
import maplibregl from 'maplibre-gl';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';

interface MapLibreDrawingProps {
  map: maplibregl.Map;
  onDrawCreate?: (feature: any) => void;
  onDrawUpdate?: (feature: any) => void;
  onDrawDelete?: (feature: any) => void;
}

export interface MapLibreDrawingRef {
  changeMode: (mode: string) => void;
  deleteAll: () => void;
  getAll: () => any;
  add: (feature: any) => void;
  deleteFeature: (id: string) => void;
}

const MapLibreDrawing = React.forwardRef<MapLibreDrawingRef, MapLibreDrawingProps>(({
  map,
  onDrawCreate,
  onDrawUpdate,
  onDrawDelete
}, ref) => {
  const drawRef = useRef<MapboxDraw | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize drawing controls
  useEffect(() => {
    if (!map || drawRef.current) return;

    try {
      // Create MapboxDraw instance with custom styles
      drawRef.current = new MapboxDraw({
        displayControlsDefault: false,
        controls: {
          point: true,
          line_string: true,
          polygon: true,
          trash: true
        },
        styles: [
          // Polygon fill
          {
            id: 'gl-draw-polygon-fill-inactive',
            type: 'fill',
            filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
            paint: {
              'fill-color': '#3bb2d0',
              'fill-outline-color': '#3bb2d0',
              'fill-opacity': 0.1
            }
          },
          {
            id: 'gl-draw-polygon-fill-active',
            type: 'fill',
            filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'Polygon']],
            paint: {
              'fill-color': '#fbb03b',
              'fill-outline-color': '#fbb03b',
              'fill-opacity': 0.1
            }
          },
          // Polygon stroke
          {
            id: 'gl-draw-polygon-stroke-inactive',
            type: 'line',
            filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
            layout: {
              'line-cap': 'round',
              'line-join': 'round'
            },
            paint: {
              'line-color': '#3bb2d0',
              'line-width': 2
            }
          },
          {
            id: 'gl-draw-polygon-stroke-active',
            type: 'line',
            filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'Polygon']],
            layout: {
              'line-cap': 'round',
              'line-join': 'round'
            },
            paint: {
              'line-color': '#fbb03b',
              'line-width': 2
            }
          },
          // Line string
          {
            id: 'gl-draw-line-inactive',
            type: 'line',
            filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'LineString'], ['!=', 'mode', 'static']],
            layout: {
              'line-cap': 'round',
              'line-join': 'round'
            },
            paint: {
              'line-color': '#3bb2d0',
              'line-width': 2
            }
          },
          {
            id: 'gl-draw-line-active',
            type: 'line',
            filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'LineString']],
            layout: {
              'line-cap': 'round',
              'line-join': 'round'
            },
            paint: {
              'line-color': '#fbb03b',
              'line-width': 2
            }
          },
          // Points
          {
            id: 'gl-draw-point-point-stroke-inactive',
            type: 'circle',
            filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Point'], ['==', 'meta', 'feature'], ['!=', 'mode', 'static']],
            paint: {
              'circle-radius': 5,
              'circle-opacity': 1,
              'circle-color': '#fff'
            }
          },
          {
            id: 'gl-draw-point-inactive',
            type: 'circle',
            filter: ['all', ['==', 'active', 'false'], ['==', '$type', 'Point'], ['==', 'meta', 'feature'], ['!=', 'mode', 'static']],
            paint: {
              'circle-radius': 3,
              'circle-color': '#3bb2d0'
            }
          },
          {
            id: 'gl-draw-point-stroke-active',
            type: 'circle',
            filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'Point'], ['==', 'meta', 'feature']],
            paint: {
              'circle-radius': 7,
              'circle-color': '#fff'
            }
          },
          {
            id: 'gl-draw-point-active',
            type: 'circle',
            filter: ['all', ['==', 'active', 'true'], ['==', '$type', 'Point'], ['==', 'meta', 'feature']],
            paint: {
              'circle-radius': 5,
              'circle-color': '#fbb03b'
            }
          },
          // Vertex points
          {
            id: 'gl-draw-polygon-and-line-vertex-stroke-inactive',
            type: 'circle',
            filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
            paint: {
              'circle-radius': 5,
              'circle-color': '#fff'
            }
          },
          {
            id: 'gl-draw-polygon-and-line-vertex-inactive',
            type: 'circle',
            filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
            paint: {
              'circle-radius': 3,
              'circle-color': '#fbb03b'
            }
          }
        ]
      });

      // Add drawing control to map
      map.addControl(drawRef.current, 'top-left');

      // Set up event listeners
      map.on('draw.create', (e) => {
        console.log('Draw create:', e);
        if (onDrawCreate) {
          onDrawCreate(e.features[0]);
        }
      });

      map.on('draw.update', (e) => {
        console.log('Draw update:', e);
        if (onDrawUpdate) {
          onDrawUpdate(e.features[0]);
        }
      });

      map.on('draw.delete', (e) => {
        console.log('Draw delete:', e);
        if (onDrawDelete) {
          onDrawDelete(e.features[0]);
        }
      });

      setIsInitialized(true);
      console.log('MapLibre drawing tools initialized');

    } catch (error) {
      console.error('Failed to initialize drawing tools:', error);
    }

    return () => {
      if (drawRef.current && map) {
        try {
          map.removeControl(drawRef.current);
          drawRef.current = null;
        } catch (error) {
          console.warn('Error removing drawing control:', error);
        }
      }
    };
  }, [map, onDrawCreate, onDrawUpdate, onDrawDelete]);

  // Public methods for controlling drawing modes
  const changeMode = (mode: string) => {
    if (drawRef.current) {
      drawRef.current.changeMode(mode);
    }
  };

  const deleteAll = () => {
    if (drawRef.current) {
      drawRef.current.deleteAll();
    }
  };

  const getAll = () => {
    if (drawRef.current) {
      return drawRef.current.getAll();
    }
    return null;
  };

  const add = (feature: any) => {
    if (drawRef.current) {
      drawRef.current.add(feature);
    }
  };

  const deleteFeature = (id: string) => {
    if (drawRef.current) {
      drawRef.current.delete(id);
    }
  };

  // Expose methods through ref
  React.useImperativeHandle(ref, () => ({
    changeMode,
    deleteAll,
    getAll,
    add,
    deleteFeature
  }));

  return null; // This component doesn't render anything directly
});

MapLibreDrawing.displayName = 'MapLibreDrawing';

export default MapLibreDrawing;
