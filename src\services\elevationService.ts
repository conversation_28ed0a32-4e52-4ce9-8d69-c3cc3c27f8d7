/**
 * Professional Elevation Service for Accurate Viewshed Analysis
 * Uses ONLY real elevation data from verified sources
 * NO simulation or dummy data - fails gracefully when real data unavailable
 */

export interface ElevationPoint {
  latitude: number;
  longitude: number;
  elevation: number; // meters above sea level
  dataSource: 'real-api' | 'unavailable';
  accuracy: 'high' | 'medium' | 'unknown';
}

export interface ElevationProfile {
  points: ElevationPoint[];
  distance: number; // total distance in meters
  resolution: number; // meters between points
  dataQuality: 'all-real' | 'partial-real' | 'no-real-data';
}

export interface ViewshedResult {
  visible: boolean;
  distance: number;
  elevation: number;
  dataSource: 'real-api' | 'unavailable';
  accuracy: 'high' | 'medium' | 'low' | 'unknown';
  obstruction?: {
    distance: number;
    elevation: number;
    height: number;
  };
}

export interface ElevationServiceStatus {
  available: boolean;
  source: string;
  lastSuccessful: Date | null;
  errorCount: number;
  rateLimited: boolean;
}

/**
 * Professional Elevation Service Class
 * ONLY uses real elevation data - no simulation
 */
export class ElevationService {
  private elevationCache = new Map<string, { elevation: number; source: string; accuracy: string }>();
  private readonly CACHE_EXPIRY = 1000 * 60 * 60 * 24; // 24 hours for real data
  private cacheTimestamps = new Map<string, number>();
  private serviceStatus: ElevationServiceStatus = {
    available: false,
    source: 'unknown',
    lastSuccessful: null,
    errorCount: 0,
    rateLimited: false
  };

  /**
   * Get elevation for a single point - REAL DATA ONLY
   * Returns null if real elevation data is unavailable
   */
  async getElevation(
    latitude: number,
    longitude: number,
    source: 'usgs' | 'open-elevation' | 'open-meteo' = 'open-meteo'
  ): Promise<ElevationPoint | null> {
    const cacheKey = `${latitude.toFixed(6)},${longitude.toFixed(6)},${source}`;

    // Check cache first
    if (this.elevationCache.has(cacheKey)) {
      const timestamp = this.cacheTimestamps.get(cacheKey) || 0;
      if (Date.now() - timestamp < this.CACHE_EXPIRY) {
        const cached = this.elevationCache.get(cacheKey)!;
        console.log(`📦 Using cached REAL elevation: ${cached.elevation}m from ${cached.source}`);
        return {
          latitude,
          longitude,
          elevation: cached.elevation,
          dataSource: 'real-api',
          accuracy: cached.accuracy as 'high' | 'medium' | 'unknown'
        };
      }
    }

    console.log(`🔍 Fetching REAL elevation data for ${latitude}, ${longitude} from ${source}`);

    try {
      const result = await this.fetchRealElevation(latitude, longitude, source);

      if (result) {
        // Cache the real result
        this.elevationCache.set(cacheKey, {
          elevation: result.elevation,
          source: result.dataSource,
          accuracy: result.accuracy
        });
        this.cacheTimestamps.set(cacheKey, Date.now());

        // Update service status
        this.serviceStatus.available = true;
        this.serviceStatus.lastSuccessful = new Date();
        this.serviceStatus.source = source;
        this.serviceStatus.errorCount = 0;

        console.log(`✅ Real elevation data: ${result.elevation}m from ${source}`);
        return result;
      } else {
        throw new Error('No real elevation data available');
      }
    } catch (error) {
      console.error(`❌ Failed to get REAL elevation data from ${source}:`, error);

      // Update service status
      this.serviceStatus.errorCount++;
      this.serviceStatus.available = false;

      // DO NOT return fake data - return null to indicate failure
      return null;
    }
  }

  /**
   * Get elevation profile between two points - REAL DATA ONLY
   */
  async getElevationProfile(
    startLat: number,
    startLng: number,
    endLat: number,
    endLng: number,
    resolution: number = 100, // meters between sample points
    source: 'usgs' | 'open-elevation' | 'open-meteo' = 'open-meteo'
  ): Promise<ElevationProfile | null> {
    const distance = this.calculateDistance(startLat, startLng, endLat, endLng);
    const numPoints = Math.max(2, Math.ceil(distance / resolution));
    const points: ElevationPoint[] = [];
    let realDataCount = 0;

    console.log(`🗺️ Generating elevation profile: ${numPoints} points over ${Math.round(distance)}m`);

    for (let i = 0; i < numPoints; i++) {
      const ratio = i / (numPoints - 1);
      const lat = startLat + (endLat - startLat) * ratio;
      const lng = startLng + (endLng - startLng) * ratio;

      const elevationPoint = await this.getElevation(lat, lng, source);

      if (elevationPoint) {
        points.push(elevationPoint);
        realDataCount++;
      } else {
        // If we can't get real elevation data, we cannot provide accurate profile
        console.warn(`❌ Failed to get real elevation at point ${i+1}/${numPoints}`);
        points.push({
          latitude: lat,
          longitude: lng,
          elevation: 0, // Placeholder - marked as unavailable
          dataSource: 'unavailable',
          accuracy: 'unknown'
        });
      }
    }

    // Determine data quality
    let dataQuality: 'all-real' | 'partial-real' | 'no-real-data';
    if (realDataCount === numPoints) {
      dataQuality = 'all-real';
    } else if (realDataCount > 0) {
      dataQuality = 'partial-real';
    } else {
      dataQuality = 'no-real-data';
      console.error('❌ No real elevation data available for profile - cannot provide accurate analysis');
      return null; // Cannot provide accurate profile without real data
    }

    console.log(`📊 Profile quality: ${dataQuality} (${realDataCount}/${numPoints} real points)`);

    return {
      points,
      distance,
      resolution: distance / (numPoints - 1),
      dataQuality
    };
  }

  /**
   * Calculate line-of-sight visibility between two points - REAL DATA ONLY
   * Uses professional-grade algorithms with Earth curvature correction
   */
  async calculateLineOfSight(
    observerLat: number,
    observerLng: number,
    observerHeight: number,
    targetLat: number,
    targetLng: number,
    targetHeight: number,
    source: 'usgs' | 'open-elevation' | 'open-meteo' = 'open-meteo'
  ): Promise<ViewshedResult | null> {
    console.log(`🔍 Calculating line-of-sight with REAL elevation data`);

    const profile = await this.getElevationProfile(
      observerLat, observerLng, targetLat, targetLng, 50, source
    );

    if (!profile || profile.dataQuality === 'no-real-data') {
      console.error('❌ Cannot calculate accurate line-of-sight without real elevation data');
      return null;
    }

    if (profile.points.length < 2) {
      return {
        visible: false,
        distance: 0,
        elevation: 0,
        dataSource: 'unavailable',
        accuracy: 'unknown'
      };
    }

    const observerPoint = profile.points[0];
    const targetPoint = profile.points[profile.points.length - 1];

    // Only proceed if we have real data for observer and target
    if (observerPoint.dataSource !== 'real-api' || targetPoint.dataSource !== 'real-api') {
      console.error('❌ Observer or target elevation data not available from real sources');
      return null;
    }

    const observerElevation = observerPoint.elevation + observerHeight;
    const targetElevation = targetPoint.elevation + targetHeight;
    const totalDistance = profile.distance;

    console.log(`📏 Observer: ${observerElevation}m, Target: ${targetElevation}m, Distance: ${Math.round(totalDistance)}m`);

    // Professional line-of-sight calculation with Earth curvature
    for (let i = 1; i < profile.points.length - 1; i++) {
      const point = profile.points[i];

      // Skip points without real elevation data
      if (point.dataSource !== 'real-api') {
        console.warn(`⚠️ Skipping point ${i} - no real elevation data`);
        continue;
      }

      const distanceFromObserver = this.calculateDistance(
        observerLat, observerLng, point.latitude, point.longitude
      );

      // Calculate line-of-sight elevation accounting for Earth's curvature
      const ratio = distanceFromObserver / totalDistance;
      const straightLineElevation = observerElevation + (targetElevation - observerElevation) * ratio;

      // Earth curvature correction (professional formula)
      const earthRadius = 6371000; // meters
      const curvatureCorrection = Math.pow(distanceFromObserver, 2) / (2 * earthRadius);
      const requiredElevation = straightLineElevation - curvatureCorrection;

      // Check if terrain blocks the line-of-sight
      if (point.elevation > requiredElevation) {
        console.log(`❌ Line-of-sight blocked at ${Math.round(distanceFromObserver)}m by terrain at ${point.elevation}m`);
        return {
          visible: false,
          distance: distanceFromObserver,
          elevation: point.elevation,
          dataSource: 'real-api',
          accuracy: point.accuracy,
          obstruction: {
            distance: distanceFromObserver,
            elevation: point.elevation,
            height: point.elevation - requiredElevation
          }
        };
      }
    }

    console.log(`✅ Line-of-sight clear to target`);
    return {
      visible: true,
      distance: totalDistance,
      elevation: targetElevation,
      dataSource: 'real-api',
      accuracy: targetPoint.accuracy
    };
  }

  /**
   * Fetch real elevation data from verified APIs
   * NO simulation or fake data - returns null if real data unavailable
   */
  private async fetchRealElevation(
    latitude: number,
    longitude: number,
    source: 'usgs' | 'open-elevation' | 'open-meteo'
  ): Promise<ElevationPoint | null> {

    switch (source) {
      case 'open-meteo':
        return this.getOpenMeteoElevation(latitude, longitude);
      case 'open-elevation':
        return this.getOpenElevationAPI(latitude, longitude);
      case 'usgs':
        return this.getUSGSElevation(latitude, longitude);
      default:
        throw new Error(`Unknown elevation source: ${source}`);
    }
  }

  /**
   * Open-Meteo Elevation API (most reliable, no CORS issues)
   */
  private async getOpenMeteoElevation(latitude: number, longitude: number): Promise<ElevationPoint | null> {
    try {
      const response = await fetch(
        `https://api.open-meteo.com/v1/elevation?latitude=${latitude}&longitude=${longitude}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          signal: AbortSignal.timeout(10000) // 10 second timeout
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.elevation && Array.isArray(data.elevation) && data.elevation.length > 0) {
        const elevation = Math.round(data.elevation[0]);

        // Validate elevation is reasonable
        if (elevation < -500 || elevation > 9000) {
          console.warn(`⚠️ Suspicious elevation value: ${elevation}m at ${latitude}, ${longitude}`);
        }

        return {
          latitude,
          longitude,
          elevation,
          dataSource: 'real-api',
          accuracy: 'high' // Open-Meteo uses high-quality DEM data
        };
      } else {
        throw new Error('Invalid response format from Open-Meteo API');
      }
    } catch (error) {
      console.error(`❌ Open-Meteo API failed for ${latitude}, ${longitude}:`, error);
      return null;
    }
  }

  /**
   * Open Elevation API (backup option)
   */
  private async getOpenElevationAPI(latitude: number, longitude: number): Promise<ElevationPoint | null> {
    try {
      const response = await fetch(
        `https://api.open-elevation.com/api/v1/lookup?locations=${latitude},${longitude}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          signal: AbortSignal.timeout(15000) // 15 second timeout
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.results && data.results[0] && typeof data.results[0].elevation === 'number') {
        const elevation = Math.round(data.results[0].elevation);

        return {
          latitude,
          longitude,
          elevation,
          dataSource: 'real-api',
          accuracy: 'medium' // Open Elevation uses SRTM data
        };
      } else {
        throw new Error('Invalid response format from Open Elevation API');
      }
    } catch (error) {
      console.error(`❌ Open Elevation API failed for ${latitude}, ${longitude}:`, error);
      return null;
    }
  }

  /**
   * USGS Elevation API (for US locations)
   */
  private async getUSGSElevation(latitude: number, longitude: number): Promise<ElevationPoint | null> {
    try {
      // USGS Elevation Point Query Service
      const response = await fetch(
        `https://nationalmap.gov/epqs/pqs.php?x=${longitude}&y=${latitude}&units=Meters&output=json`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
          signal: AbortSignal.timeout(10000)
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.USGS_Elevation_Point_Query_Service &&
          data.USGS_Elevation_Point_Query_Service.Elevation_Query &&
          typeof data.USGS_Elevation_Point_Query_Service.Elevation_Query.Elevation === 'number') {

        const elevation = Math.round(data.USGS_Elevation_Point_Query_Service.Elevation_Query.Elevation);

        return {
          latitude,
          longitude,
          elevation,
          dataSource: 'real-api',
          accuracy: 'high' // USGS has very high accuracy for US locations
        };
      } else {
        throw new Error('Invalid response format from USGS API');
      }
    } catch (error) {
      console.error(`❌ USGS API failed for ${latitude}, ${longitude}:`, error);
      return null;
    }
  }

  /**
   * Calculate distance between two points in meters
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Get elevation service status
   */
  getServiceStatus(): ElevationServiceStatus {
    return { ...this.serviceStatus };
  }

  /**
   * Test elevation service availability
   */
  async testService(source: 'usgs' | 'open-elevation' | 'open-meteo' = 'open-meteo'): Promise<boolean> {
    console.log(`🧪 Testing elevation service: ${source}`);

    // Test with a known location (Mount Washington, NH)
    const testLat = 44.2706;
    const testLng = -71.3033;

    try {
      const result = await this.getElevation(testLat, testLng, source);
      if (result && result.dataSource === 'real-api') {
        console.log(`✅ Service test passed: ${source} returned ${result.elevation}m`);
        return true;
      } else {
        console.log(`❌ Service test failed: ${source} did not return real data`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Service test error for ${source}:`, error);
      return false;
    }
  }

  /**
   * Clear elevation cache
   */
  clearCache(): void {
    this.elevationCache.clear();
    this.cacheTimestamps.clear();
    console.log('🗑️ Elevation cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; realDataPoints: number; sources: string[] } {
    const sources = new Set<string>();
    let realDataPoints = 0;

    for (const cached of this.elevationCache.values()) {
      sources.add(cached.source);
      if (cached.source !== 'simulated') {
        realDataPoints++;
      }
    }

    return {
      size: this.elevationCache.size,
      realDataPoints,
      sources: Array.from(sources)
    };
  }

  /**
   * Validate elevation data quality
   */
  validateElevationData(points: ElevationPoint[]): {
    isValid: boolean;
    realDataPercentage: number;
    issues: string[];
  } {
    const issues: string[] = [];
    let realDataCount = 0;

    for (const point of points) {
      if (point.dataSource === 'real-api') {
        realDataCount++;
      } else {
        issues.push(`Point at ${point.latitude}, ${point.longitude} has no real elevation data`);
      }

      // Check for suspicious elevation values
      if (point.elevation < -500 || point.elevation > 9000) {
        issues.push(`Suspicious elevation ${point.elevation}m at ${point.latitude}, ${point.longitude}`);
      }
    }

    const realDataPercentage = points.length > 0 ? (realDataCount / points.length) * 100 : 0;
    const isValid = realDataPercentage >= 80; // Require at least 80% real data for valid analysis

    if (realDataPercentage < 100) {
      issues.push(`Only ${realDataPercentage.toFixed(1)}% real elevation data available`);
    }

    return {
      isValid,
      realDataPercentage,
      issues
    };
  }
}

// Export singleton instance
export const elevationService = new ElevationService();
